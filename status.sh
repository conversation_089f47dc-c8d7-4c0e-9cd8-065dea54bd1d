#!/bin/bash

# 状态检查脚本

APP_NAME="solve-api"
APP_PORT=8080
PID_FILE="/tmp/${APP_NAME}.pid"
LOG_FILE="/tmp/${APP_NAME}.log"

echo "=== $APP_NAME 状态检查 ==="

# 检查 PID 文件
if [ -f "$PID_FILE" ]; then
    PID=$(cat $PID_FILE)
    echo "PID 文件: $PID_FILE (PID: $PID)"
    
    # 检查进程是否运行
    if ps -p $PID > /dev/null 2>&1; then
        echo "✅ 进程状态: 运行中"
        
        # 检查端口监听
        if netstat -tuln | grep ":$APP_PORT " > /dev/null; then
            echo "✅ 端口状态: $APP_PORT 监听中"
        else
            echo "❌ 端口状态: $APP_PORT 未监听"
        fi
        
        # 检查 HTTP 响应
        if curl -s http://localhost:$APP_PORT/health > /dev/null; then
            echo "✅ HTTP 状态: 健康检查通过"
        else
            echo "❌ HTTP 状态: 健康检查失败"
        fi
        
        # 显示进程信息
        echo ""
        echo "进程信息:"
        ps -p $PID -o pid,ppid,cmd,etime,pcpu,pmem
        
    else
        echo "❌ 进程状态: 未运行"
        rm -f $PID_FILE
    fi
else
    echo "❌ PID 文件不存在，应用未运行"
fi

# 显示最近的日志
if [ -f "$LOG_FILE" ]; then
    echo ""
    echo "=== 最近 10 行日志 ==="
    tail -n 10 $LOG_FILE
else
    echo "❌ 日志文件不存在: $LOG_FILE"
fi
