## S1需求：标点符号规范化工具

**需求描述：**
使用一个映射表 + 字符遍历替换的方式来实现，确保不会乱码，并保持可控性。一劳永逸的方法解决标点符号转码以及防止乱码的问题。

**实现状态：✅ 已完成**

### 实现方案

1. **创建统一的标点符号规范化工具** (`utils/text_normalizer.go`)
   - 使用rune映射表确保不会乱码
   - 支持全角到半角标点符号转换
   - 提供多种规范化级别的函数

2. **替换项目中现有的多个不同实现**
   - 更新 `services/deepseek_service.go` 使用统一工具
   - 更新 `services/qwen_service.go` 使用统一工具  
   - 更新 `services/redis_cache.go` 使用统一工具

### 核心功能

- `NormalizePunctuation()`: 基础标点符号规范化
- `CleanUTF8String()`: UTF-8字符清理
- `NormalizeText()`: 综合文本规范化
- `NormalizeSpaces()`: 空格规范化
- `NormalizeForCache()`: 缓存键生成用的规范化
- `NormalizeOptions()`: 选项内容规范化

### 支持的标点符号转换

| 全角符号 | 半角符号 | 说明 |
|---------|---------|------|
| ，      | ,       | 逗号 |
| 。      | .       | 句号 |
| ！      | !       | 感叹号 |
| ？      | ?       | 问号 |
| ：      | :       | 冒号 |
| ；      | ;       | 分号 |
| "       | "       | 左双引号 |
| "       | "       | 右双引号 |
| '       | '       | 左单引号 |
| '       | '       | 右单引号 |
| （      | (       | 左圆括号 |
| ）      | )       | 右圆括号 |
| 【      | [       | 左方括号 |
| 】      | ]       | 右方括号 |
| 《      | <       | 左书名号 |
| 》      | >       | 右书名号 |
| 、      | ,       | 顿号转逗号 |
| ～      | ~       | 波浪号 |
| —       | -       | 破折号 |
| 　      | (空格)   | 全角空格转半角空格 |

### 测试结果

✅ 基础标点符号转换正常
✅ UTF-8字符清理正常  
✅ 综合文本规范化正常
✅ 缓存键生成规范化正常
✅ API集成测试通过

### API测试数据

- **API Key**: `ak_27a6480d6a695cb53b6a0a955e2930d9b639a3d225ebb10fdefa8f33bb67d8eb`
- **测试图片**: `https://www.bonuspoints.uzdns.com/20250604132258f988d2353.jpg`
- **测试结果**: ✅ 成功，标点符号规范化工作正常

### 实现细节

#### 原始需求代码示例
```go
var punctuationMap = map[rune]rune{
    '，': ',', '。': '.', '！': '!', '？': '?',
    '：': ':', '；': ';', '"': '"', '"': '"',
    ''': '\'', ''': '\'', '（': '(', '）': ')',
    '【': '[', '】': ']', '《': '<', '》': '>',
    '、': ',', '～': '~', '—': '-', '　': ' ',
}

func NormalizePunctuation(text string) string {
    var builder strings.Builder
    for _, ch := range text {
        if mapped, ok := punctuationMap[ch]; ok {
            builder.WriteRune(mapped)
        } else {
            builder.WriteRune(ch)
        }
    }
    return builder.String()
}
```

#### 增强版实现
在 `utils/text_normalizer.go` 中实现了更完善的版本，包括：
- 更全面的标点符号映射
- UTF-8字符清理
- 多级文本规范化
- 性能优化（预分配内存）

### 使用方法

```go
import "solve-api/utils"

// 基础标点符号规范化
text := utils.NormalizePunctuation("你好，世界！")

// 综合文本规范化
text := utils.NormalizeText("你好，世界！　　")

// 缓存键生成用规范化
key := utils.NormalizeForCache("题目内容，选项A")
```

### 项目集成

该工具已成功集成到项目的以下模块：
1. Qwen API 响应处理
2. DeepSeek API 请求预处理
3. Redis 缓存键生成
4. 题目选项内容规范化

所有文本处理现在都使用统一的规范化工具，确保了一致性和可维护性。
