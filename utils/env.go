package utils

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"strings"
)

// LoadEnvFile 从.env文件加载环境变量
func LoadEnvFile(filename string) error {
	file, err := os.Open(filename)
	if err != nil {
		return fmt.Errorf("无法打开环境变量文件 %s: %v", filename, err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	lineNum := 0
	
	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())
		
		// 跳过空行和注释
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}
		
		// 解析 KEY=VALUE 格式
		parts := strings.SplitN(line, "=", 2)
		if len(parts) != 2 {
			log.Printf("警告: 第%d行格式错误，跳过: %s", lineNum, line)
			continue
		}
		
		key := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])
		
		// 移除值两端的引号（如果有）
		if len(value) >= 2 {
			if (strings.HasPrefix(value, "\"") && strings.HasSuffix(value, "\"")) ||
				(strings.HasPrefix(value, "'") && strings.HasSuffix(value, "'")) {
				value = value[1 : len(value)-1]
			}
		}
		
		// 设置环境变量（不覆盖已存在的）
		if os.Getenv(key) == "" {
			err := os.Setenv(key, value)
			if err != nil {
				log.Printf("警告: 无法设置环境变量 %s: %v", key, err)
			} else {
				log.Printf("加载环境变量: %s=%s", key, maskSensitiveValue(key, value))
			}
		} else {
			log.Printf("环境变量已存在，跳过: %s", key)
		}
	}
	
	if err := scanner.Err(); err != nil {
		return fmt.Errorf("读取环境变量文件时出错: %v", err)
	}
	
	return nil
}

// maskSensitiveValue 遮蔽敏感信息
func maskSensitiveValue(key, value string) string {
	sensitiveKeys := []string{
		"PASSWORD", "SECRET", "KEY", "TOKEN", "PASS",
	}
	
	keyUpper := strings.ToUpper(key)
	for _, sensitive := range sensitiveKeys {
		if strings.Contains(keyUpper, sensitive) {
			if len(value) <= 4 {
				return "****"
			}
			return value[:2] + "****" + value[len(value)-2:]
		}
	}
	
	return value
}

// GetEnvWithDefault 获取环境变量，如果不存在则返回默认值
func GetEnvWithDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// MustGetEnv 获取必需的环境变量，如果不存在则panic
func MustGetEnv(key string) string {
	value := os.Getenv(key)
	if value == "" {
		panic(fmt.Sprintf("必需的环境变量 %s 未设置", key))
	}
	return value
}

// PrintEnvStatus 打印环境变量状态
func PrintEnvStatus() {
	log.Println("=== 环境变量状态 ===")
	
	envVars := []string{
		"DB_HOST", "DB_PORT", "DB_USER", "DB_PASSWORD", "DB_NAME",
		"REDIS_HOST", "REDIS_PORT", "REDIS_PASSWORD", "REDIS_DB",
		"SERVER_HOST", "SERVER_PORT", "GIN_MODE",
		"QWEN_API_KEY", "DEEPSEEK_API_KEY",
		"LOG_LEVEL", "CACHE_TTL",
	}
	
	for _, key := range envVars {
		value := os.Getenv(key)
		if value != "" {
			log.Printf("✅ %s = %s", key, maskSensitiveValue(key, value))
		} else {
			log.Printf("❌ %s = (未设置)", key)
		}
	}
	
	log.Println("==================")
}
