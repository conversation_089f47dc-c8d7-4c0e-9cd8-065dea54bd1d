package utils

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"
)

// APILogger API专用日志记录器
type APILogger struct {
	logger *log.Logger
	file   *os.File
}

// PerformanceStats 性能统计数据
type PerformanceStats struct {
	QwenDuration     time.Duration // Qwen API调用耗时
	DeepSeekDuration time.Duration // DeepSeek API调用耗时
	TotalDuration    time.Duration // 总耗时
	QwenTokens       TokenUsage    // Qwen Token使用量
	DeepSeekTokens   TokenUsage    // DeepSeek Token使用量
}

// TokenUsage Token使用量统计
type TokenUsage struct {
	InputTokens  int // 输入Token数
	OutputTokens int // 输出Token数
	TotalTokens  int // 总Token数
}

var (
	// 全局API日志记录器实例
	apiLogger *APILogger
)

// InitAPILogger 初始化API日志记录器
func InitAPILogger() error {
	// 创建logs目录
	logsDir := "logs"
	if err := os.MkdirAll(logsDir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %v", err)
	}

	// 创建API日志文件
	logFile := filepath.Join(logsDir, "api_details.log")
	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return fmt.Errorf("创建API日志文件失败: %v", err)
	}

	// 创建日志记录器
	logger := log.New(file, "", log.LstdFlags|log.Lmicroseconds)

	apiLogger = &APILogger{
		logger: logger,
		file:   file,
	}

	// 记录初始化信息
	apiLogger.logger.Println("=== API详细日志记录器初始化成功 ===")
	
	return nil
}

// CloseAPILogger 关闭API日志记录器
func CloseAPILogger() {
	if apiLogger != nil && apiLogger.file != nil {
		apiLogger.file.Close()
	}
}

// LogQwenRawResponse 记录Qwen返回的原始数据
func LogQwenRawResponse(imageURL, rawResponse string) {
	if apiLogger == nil {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	apiLogger.logger.Printf("\n" +
		"==================== QWEN 原始响应 ====================\n" +
		"时间: %s\n" +
		"图片URL: %s\n" +
		"原始响应数据:\n%s\n" +
		"======================================================\n",
		timestamp, imageURL, rawResponse)
}

// LogQwenRawResponseWithStats 记录Qwen返回的原始数据（包含性能统计）
func LogQwenRawResponseWithStats(imageURL, rawResponse string, qwenDuration time.Duration, tokens TokenUsage) {
	if apiLogger == nil {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	apiLogger.logger.Printf("\n" +
		"==================== QWEN 原始响应 ====================\n" +
		"时间: %s\n" +
		"图片URL: %s\n" +
		"Qwen API耗时: %v (%.2f秒)\n" +
		"Token使用量: 输入=%d, 输出=%d, 总计=%d\n" +
		"原始响应数据:\n%s\n" +
		"======================================================\n",
		timestamp, imageURL, qwenDuration, qwenDuration.Seconds(),
		tokens.InputTokens, tokens.OutputTokens, tokens.TotalTokens, rawResponse)
}

// LogDeepSeekRawResponse 记录DeepSeek返回的原始数据
func LogDeepSeekRawResponse(questionData, rawResponse string) {
	if apiLogger == nil {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	apiLogger.logger.Printf("\n" +
		"================== DEEPSEEK 原始响应 ==================\n" +
		"时间: %s\n" +
		"输入题目数据: %s\n" +
		"原始响应数据:\n%s\n" +
		"======================================================\n",
		timestamp, questionData, rawResponse)
}

// LogDeepSeekRawResponseWithStats 记录DeepSeek返回的原始数据（包含性能统计）
func LogDeepSeekRawResponseWithStats(questionData, rawResponse string, deepseekDuration time.Duration, tokens TokenUsage) {
	if apiLogger == nil {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	apiLogger.logger.Printf("\n" +
		"================== DEEPSEEK 原始响应 ==================\n" +
		"时间: %s\n" +
		"DeepSeek API耗时: %v (%.2f秒)\n" +
		"Token使用量: 输入=%d, 输出=%d, 总计=%d\n" +
		"输入题目数据: %s\n" +
		"原始响应数据:\n%s\n" +
		"======================================================\n",
		timestamp, deepseekDuration, deepseekDuration.Seconds(),
		tokens.InputTokens, tokens.OutputTokens, tokens.TotalTokens, questionData, rawResponse)
}

// LogDeepSeekParsedJSON 记录DeepSeek原始数据解析后的JSON
func LogDeepSeekParsedJSON(rawResponse, answer, analysis string) {
	if apiLogger == nil {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	
	// 构建解析后的数据结构
	parsedData := map[string]interface{}{
		"answer":   answer,
		"analysis": analysis,
	}
	
	// 转换为格式化的JSON
	jsonData, err := json.MarshalIndent(parsedData, "", "  ")
	if err != nil {
		jsonData = []byte(fmt.Sprintf("JSON序列化失败: %v", err))
	}

	apiLogger.logger.Printf("\n" +
		"=============== DEEPSEEK 解析后JSON ==================\n" +
		"时间: %s\n" +
		"原始响应: %s\n" +
		"解析后JSON:\n%s\n" +
		"======================================================\n",
		timestamp, rawResponse, string(jsonData))
}

// LogAPICallSummary 记录API调用汇总信息
func LogAPICallSummary(imageURL, qwenResponse, deepseekResponse, finalAnswer, finalAnalysis string) {
	if apiLogger == nil {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	apiLogger.logger.Printf("\n" +
		"=================== API调用汇总 ======================\n" +
		"时间: %s\n" +
		"图片URL: %s\n" +
		"Qwen响应长度: %d 字符\n" +
		"DeepSeek响应长度: %d 字符\n" +
		"最终答案: %s\n" +
		"最终解析长度: %d 字符\n" +
		"======================================================\n",
		timestamp, imageURL, len(qwenResponse), len(deepseekResponse), finalAnswer, len(finalAnalysis))
}

// LogAPICallSummaryWithStats 记录API调用汇总信息（包含完整性能统计）
func LogAPICallSummaryWithStats(imageURL, qwenResponse, deepseekResponse, finalAnswer, finalAnalysis string, stats PerformanceStats) {
	if apiLogger == nil {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	apiLogger.logger.Printf("\n" +
		"=================== API调用汇总 ======================\n" +
		"时间: %s\n" +
		"图片URL: %s\n" +
		"=== 性能统计 ===\n" +
		"Qwen API耗时: %v (%.2f秒)\n" +
		"DeepSeek API耗时: %v (%.2f秒)\n" +
		"总业务耗时: %v (%.2f秒)\n" +
		"=== Token使用量 ===\n" +
		"Qwen Token: 输入=%d, 输出=%d, 总计=%d\n" +
		"DeepSeek Token: 输入=%d, 输出=%d, 总计=%d\n" +
		"=== 响应数据 ===\n" +
		"Qwen响应长度: %d 字符\n" +
		"DeepSeek响应长度: %d 字符\n" +
		"最终答案: %s\n" +
		"最终解析长度: %d 字符\n" +
		"======================================================\n",
		timestamp, imageURL,
		stats.QwenDuration, stats.QwenDuration.Seconds(),
		stats.DeepSeekDuration, stats.DeepSeekDuration.Seconds(),
		stats.TotalDuration, stats.TotalDuration.Seconds(),
		stats.QwenTokens.InputTokens, stats.QwenTokens.OutputTokens, stats.QwenTokens.TotalTokens,
		stats.DeepSeekTokens.InputTokens, stats.DeepSeekTokens.OutputTokens, stats.DeepSeekTokens.TotalTokens,
		len(qwenResponse), len(deepseekResponse), finalAnswer, len(finalAnalysis))
}

// LogError 记录错误信息
func LogError(operation, errorMsg string) {
	if apiLogger == nil {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	apiLogger.logger.Printf("\n" +
		"===================== 错误日志 =======================\n" +
		"时间: %s\n" +
		"操作: %s\n" +
		"错误信息: %s\n" +
		"======================================================\n",
		timestamp, operation, errorMsg)
}

// LogDebug 记录调试信息
func LogDebug(operation, message string) {
	if apiLogger == nil {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	apiLogger.logger.Printf("\n" +
		"===================== 调试日志 =======================\n" +
		"时间: %s\n" +
		"操作: %s\n" +
		"信息: %s\n" +
		"======================================================\n",
		timestamp, operation, message)
}
