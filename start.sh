#!/bin/bash

# 启动脚本 - 用于生产环境

APP_NAME="solve-api"
APP_PORT=8080
PID_FILE="/tmp/${APP_NAME}.pid"
LOG_FILE="/tmp/${APP_NAME}.log"

# 检查是否已经在运行
if [ -f "$PID_FILE" ]; then
    PID=$(cat $PID_FILE)
    if ps -p $PID > /dev/null 2>&1; then
        echo "❌ $APP_NAME 已经在运行中 (PID: $PID)"
        exit 1
    else
        echo "清理过期的 PID 文件..."
        rm -f $PID_FILE
    fi
fi

# 检查二进制文件是否存在
if [ ! -f "./$APP_NAME" ]; then
    echo "❌ 二进制文件 $APP_NAME 不存在，请先运行 build.sh"
    exit 1
fi

# 复制生产环境配置
if [ -f ".env.production" ]; then
    cp .env.production .env
    echo "✅ 使用生产环境配置"
else
    echo "⚠️  警告: 未找到 .env.production 文件，使用默认配置"
fi

# 启动应用
echo "启动 $APP_NAME..."
nohup ./$APP_NAME > $LOG_FILE 2>&1 &
PID=$!

# 保存 PID
echo $PID > $PID_FILE

# 等待启动
sleep 2

# 检查是否启动成功
if ps -p $PID > /dev/null 2>&1; then
    echo "✅ $APP_NAME 启动成功!"
    echo "PID: $PID"
    echo "端口: $APP_PORT"
    echo "日志文件: $LOG_FILE"
    echo "PID文件: $PID_FILE"
    
    # 检查端口是否监听
    sleep 1
    if netstat -tuln | grep ":$APP_PORT " > /dev/null; then
        echo "✅ 端口 $APP_PORT 监听正常"
    else
        echo "⚠️  警告: 端口 $APP_PORT 可能未正常监听"
    fi
else
    echo "❌ $APP_NAME 启动失败!"
    rm -f $PID_FILE
    exit 1
fi
