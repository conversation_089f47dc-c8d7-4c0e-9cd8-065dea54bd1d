package handlers

import (
	"net/http"
	"runtime"
	"solve-api/database"
	"time"

	"github.com/gin-gonic/gin"
)

// HealthStatus 健康状态
type HealthStatus struct {
	Status    string                 `json:"status"`
	Timestamp time.Time              `json:"timestamp"`
	Version   string                 `json:"version"`
	Uptime    string                 `json:"uptime"`
	Database  DatabaseHealth         `json:"database"`
	System    SystemHealth           `json:"system"`
	Services  map[string]interface{} `json:"services"`
}

// DatabaseHealth 数据库健康状态
type DatabaseHealth struct {
	Status      string `json:"status"`
	Connections int    `json:"connections"`
	MaxOpen     int    `json:"max_open"`
	MaxIdle     int    `json:"max_idle"`
}

// SystemHealth 系统健康状态
type SystemHealth struct {
	Goroutines   int     `json:"goroutines"`
	MemoryUsage  uint64  `json:"memory_usage_mb"`
	CPUCount     int     `json:"cpu_count"`
	GCPauseTotal float64 `json:"gc_pause_total_ms"`
}

var startTime = time.Now()

// HealthCheck 健康检查接口
func HealthCheck(c *gin.Context) {
	health := HealthStatus{
		Status:    "ok",
		Timestamp: time.Now(),
		Version:   "1.0.0", // 可以从配置或构建信息中获取
		Uptime:    time.Since(startTime).String(),
		Database:  getDatabaseHealth(),
		System:    getSystemHealth(),
		Services:  getServicesHealth(),
	}
	
	// 检查各组件状态
	if health.Database.Status != "ok" {
		health.Status = "degraded"
	}
	
	statusCode := http.StatusOK
	if health.Status != "ok" {
		statusCode = http.StatusServiceUnavailable
	}
	
	c.JSON(statusCode, health)
}

// getDatabaseHealth 获取数据库健康状态
func getDatabaseHealth() DatabaseHealth {
	sqlDB, err := database.DB.DB()
	if err != nil {
		return DatabaseHealth{
			Status: "error",
		}
	}
	
	// 测试数据库连接
	if err := sqlDB.Ping(); err != nil {
		return DatabaseHealth{
			Status: "error",
		}
	}
	
	stats := sqlDB.Stats()
	
	return DatabaseHealth{
		Status:      "ok",
		Connections: stats.OpenConnections,
		MaxOpen:     stats.MaxOpenConnections,
		MaxIdle:     int(stats.MaxIdleClosed),
	}
}

// getSystemHealth 获取系统健康状态
func getSystemHealth() SystemHealth {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	return SystemHealth{
		Goroutines:   runtime.NumGoroutine(),
		MemoryUsage:  m.Alloc / 1024 / 1024, // 转换为MB
		CPUCount:     runtime.NumCPU(),
		GCPauseTotal: float64(m.PauseTotalNs) / 1e6, // 转换为毫秒
	}
}

// getServicesHealth 获取服务健康状态
func getServicesHealth() map[string]interface{} {
	services := make(map[string]interface{})
	
	// 可以添加对外部服务的健康检查
	services["qwen_api"] = map[string]interface{}{
		"status": "unknown", // 可以实际测试API连接
	}
	
	services["deepseek_api"] = map[string]interface{}{
		"status": "unknown", // 可以实际测试API连接
	}
	
	return services
}

// ReadinessCheck 就绪检查接口
func ReadinessCheck(c *gin.Context) {
	// 检查关键依赖是否就绪
	if !isDatabaseReady() {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status": "not_ready",
			"reason": "database_not_ready",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"status": "ready",
	})
}

// LivenessCheck 存活检查接口
func LivenessCheck(c *gin.Context) {
	// 简单的存活检查
	c.JSON(http.StatusOK, gin.H{
		"status": "alive",
		"timestamp": time.Now(),
	})
}

// isDatabaseReady 检查数据库是否就绪
func isDatabaseReady() bool {
	sqlDB, err := database.DB.DB()
	if err != nil {
		return false
	}
	
	return sqlDB.Ping() == nil
}
