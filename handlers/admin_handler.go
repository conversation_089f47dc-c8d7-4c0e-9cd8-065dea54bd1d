package handlers

import (
	"crypto/rand"
	"encoding/hex"
	"net/http"
	"solve-api/database"
	"solve-api/models"
	"solve-api/services"
	"solve-api/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

// AdminHandler 管理员处理器
type AdminHandler struct{
	hybridCache *services.HybridCache
}

// NewAdminHandler 创建管理员处理器实例
func NewAdminHandler() *AdminHandler {
	return &AdminHandler{
		hybridCache: services.NewHybridCache(),
	}
}

// Login 管理员登录
func (h *AdminHandler) Login(c *gin.Context) {
	var req models.AdminLoginRequest
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    400,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 查找管理员用户
	var admin models.AdminUser
	if err := database.DB.Where("username = ? AND status = ?", req.Username, "active").First(&admin).Error; err != nil {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Code:    401,
			Message: "用户名或密码错误",
		})
		return
	}

	// 验证密码
	if !utils.CheckPassword(req.Password, admin.Password) {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Code:    401,
			Message: "用户名或密码错误",
		})
		return
	}

	// 生成 JWT token
	token, err := utils.GenerateToken(admin.ID, admin.Username, admin.Role)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    500,
			Message: "生成 Token 失败",
			Error:   err.Error(),
		})
		return
	}

	// 返回登录成功响应
	var response models.AdminLoginResponse
	response.Code = 200
	response.Message = "登录成功"
	response.Data.Token = token
	response.Data.Username = admin.Username
	response.Data.Role = admin.Role

	c.JSON(http.StatusOK, response)
}

// CreateAPIKey 创建 API Key
func (h *AdminHandler) CreateAPIKey(c *gin.Context) {
	var req models.CreateAPIKeyRequest
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    400,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 设置默认配额
	if req.QuotaLimit <= 0 {
		req.QuotaLimit = 1000
	}

	// 生成 API Key
	apiKey := generateAPIKey()

	// 创建 API Key 记录
	key := models.APIKey{
		Key:         apiKey,
		Status:      "active",
		QuotaLimit:  req.QuotaLimit,
		QuotaUsed:   0,
		IPWhitelist: req.IPWhitelist,
	}

	if err := database.DB.Create(&key).Error; err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    500,
			Message: "创建 API Key 失败",
			Error:   err.Error(),
		})
		return
	}

	// 返回创建成功响应
	var response models.CreateAPIKeyResponse
	response.Code = 200
	response.Message = "创建成功"
	response.Data.ID = key.ID
	response.Data.Key = key.Key
	response.Data.QuotaLimit = key.QuotaLimit
	response.Data.IPWhitelist = key.IPWhitelist

	c.JSON(http.StatusOK, response)
}

// ListAPIKeys 获取 API Key 列表
func (h *AdminHandler) ListAPIKeys(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize

	var total int64
	var keys []models.APIKey

	// 获取总数
	database.DB.Model(&models.APIKey{}).Count(&total)

	// 获取分页数据
	if err := database.DB.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&keys).Error; err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    500,
			Message: "获取 API Key 列表失败",
			Error:   err.Error(),
		})
		return
	}

	// 返回列表响应
	var response models.APIKeyListResponse
	response.Code = 200
	response.Message = "获取成功"
	response.Data.Total = int(total)
	response.Data.Items = keys

	c.JSON(http.StatusOK, response)
}

// UpdateAPIKeyStatus 更新 API Key 状态
func (h *AdminHandler) UpdateAPIKeyStatus(c *gin.Context) {
	id := c.Param("id")
	status := c.Query("status")

	if status != "active" && status != "revoked" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    400,
			Message: "状态参数错误，只能是 active 或 revoked",
		})
		return
	}

	if err := database.DB.Model(&models.APIKey{}).Where("id = ?", id).Update("status", status).Error; err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    500,
			Message: "更新状态失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.SolveResponse{
		Code:    200,
		Message: "状态更新成功",
	})
}

// ListAPICallLogs 获取 API 调用日志列表
func (h *AdminHandler) ListAPICallLogs(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	apiKeyID := c.Query("api_key_id")
	
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize

	var total int64
	var logs []models.APICallLog

	query := database.DB.Model(&models.APICallLog{})
	if apiKeyID != "" {
		query = query.Where("api_key_id = ?", apiKeyID)
	}

	// 获取总数
	query.Count(&total)

	// 获取分页数据
	if err := query.Offset(offset).Limit(pageSize).Order("timestamp DESC").Find(&logs).Error; err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    500,
			Message: "获取调用日志失败",
			Error:   err.Error(),
		})
		return
	}

	// 返回列表响应
	var response models.APICallLogListResponse
	response.Code = 200
	response.Message = "获取成功"
	response.Data.Total = int(total)
	response.Data.Items = logs

	c.JSON(http.StatusOK, response)
}

// GetCacheStats 获取缓存统计信息
func (h *AdminHandler) GetCacheStats(c *gin.Context) {
	if h.hybridCache == nil {
		c.JSON(http.StatusServiceUnavailable, models.ErrorResponse{
			Code:    503,
			Message: "混合缓存服务不可用",
		})
		return
	}

	stats := h.hybridCache.GetStats()

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取混合缓存统计成功",
		"data":    stats,
	})
}

// ClearCache 清空缓存
func (h *AdminHandler) ClearCache(c *gin.Context) {
	if h.hybridCache == nil {
		c.JSON(http.StatusServiceUnavailable, models.ErrorResponse{
			Code:    503,
			Message: "混合缓存服务不可用",
		})
		return
	}

	err := h.hybridCache.ClearCache()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    500,
			Message: "清空缓存失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "混合缓存清空成功（仅清理Redis，保留MySQL持久化数据）",
	})
}

// generateAPIKey 生成随机 API Key
func generateAPIKey() string {
	bytes := make([]byte, 32)
	rand.Read(bytes)
	return "ak_" + hex.EncodeToString(bytes)
}
