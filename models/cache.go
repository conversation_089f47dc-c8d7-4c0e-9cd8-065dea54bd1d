package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// JSONMap 自定义JSON类型，用于存储map[string]string
type JSONMap map[string]string

// Value 实现driver.Valuer接口
func (j JSONMap) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现sql.Scanner接口
func (j *JSONMap) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into JSONMap", value)
	}

	return json.Unmarshal(bytes, j)
}

// QuestionCache 题目缓存模型
type QuestionCache struct {
	ID              uint      `json:"id" gorm:"primaryKey"`
	CacheKey        string    `json:"cache_key" gorm:"uniqueIndex;size:64;not null"`
	QuestionType    string    `json:"question_type" gorm:"size:20;not null;index"`
	QuestionContent string    `json:"question_content" gorm:"type:text;not null"`
	QuestionOptions JSONMap   `json:"question_options" gorm:"type:json;not null"`
	Answer          string    `json:"answer" gorm:"size:500;not null"`
	Analysis        string    `json:"analysis" gorm:"type:text;not null"`
	HitCount        int       `json:"hit_count" gorm:"default:0;index"`
	LastHitAt       *time.Time `json:"last_hit_at" gorm:"index"`
	CreatedAt       time.Time `json:"created_at" gorm:"index"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// CacheStats 缓存统计模型
type CacheStats struct {
	ID            uint      `json:"id" gorm:"primaryKey"`
	Date          time.Time `json:"date" gorm:"type:date;uniqueIndex;not null"`
	RedisHits     int       `json:"redis_hits" gorm:"default:0"`
	MySQLHits     int       `json:"mysql_hits" gorm:"default:0"`
	CacheMisses   int       `json:"cache_misses" gorm:"default:0"`
	TotalRequests int       `json:"total_requests" gorm:"default:0"`
	HitRate       float64   `json:"hit_rate" gorm:"type:decimal(5,2);default:0.00"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// ToQuestionData 转换为QuestionData格式
func (qc *QuestionCache) ToQuestionData() *QuestionData {
	return &QuestionData{
		Type:     qc.QuestionType,
		Question: qc.QuestionContent,
		Options:  map[string]string(qc.QuestionOptions),
		Answer:   qc.Answer,
		Analysis: qc.Analysis,
	}
}

// FromQuestionData 从QuestionData创建缓存记录
func (qc *QuestionCache) FromQuestionData(cacheKey string, data *QuestionData) {
	qc.CacheKey = cacheKey
	qc.QuestionType = data.Type
	qc.QuestionContent = data.Question
	qc.QuestionOptions = JSONMap(data.Options)
	qc.Answer = data.Answer
	qc.Analysis = data.Analysis
	qc.HitCount = 0
	now := time.Now()
	qc.LastHitAt = &now
}

// IncrementHit 增加命中次数
func (qc *QuestionCache) IncrementHit(db *gorm.DB) error {
	now := time.Now()
	return db.Model(qc).Updates(map[string]interface{}{
		"hit_count":    gorm.Expr("hit_count + 1"),
		"last_hit_at":  now,
		"updated_at":   now,
	}).Error
}

// CacheType 缓存类型枚举
type CacheType string

const (
	CacheTypeRedis CacheType = "redis"
	CacheTypeMySQL CacheType = "mysql"
	CacheTypeMiss  CacheType = "miss"
)

// CacheHitInfo 缓存命中信息
type CacheHitInfo struct {
	Type      CacheType `json:"type"`
	Key       string    `json:"key"`
	HitTime   time.Time `json:"hit_time"`
	Source    string    `json:"source"`
	HitCount  int       `json:"hit_count,omitempty"`
}

// TableName 指定表名
func (QuestionCache) TableName() string {
	return "question_cache"
}

// TableName 指定表名
func (CacheStats) TableName() string {
	return "cache_stats"
}
