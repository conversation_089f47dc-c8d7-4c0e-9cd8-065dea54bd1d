package models

import (
	"time"

	"gorm.io/gorm"
)

// APIKey API密钥模型
type APIKey struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Key         string         `json:"key" gorm:"uniqueIndex;size:128;comment:API Key"`
	UserID      *uint          `json:"user_id" gorm:"comment:绑定的用户ID"`
	Status      string         `json:"status" gorm:"type:enum('active','revoked');default:'active';comment:状态"`
	QuotaLimit  int            `json:"quota_limit" gorm:"default:1000;comment:每月最大调用次数"`
	QuotaUsed   int            `json:"quota_used" gorm:"default:0;comment:当前已用调用次数"`
	IPWhitelist string         `json:"ip_whitelist" gorm:"type:text;comment:白名单IP列表"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	LastUsedAt  *time.Time     `json:"last_used_at" gorm:"comment:最近调用时间"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (APIKey) TableName() string {
	return "api_keys"
}

// APICallLog API调用日志模型
type APICallLog struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	APIKeyID     uint      `json:"api_key_id" gorm:"index;comment:外键关联api_keys.id"`
	Path         string    `json:"path" gorm:"size:255;comment:请求路径"`
	Method       string    `json:"method" gorm:"size:10;comment:请求方法"`
	IP           string    `json:"ip" gorm:"size:45;comment:请求IP"`
	Timestamp    time.Time `json:"timestamp" gorm:"comment:调用时间"`
	StatusCode   int       `json:"status_code" gorm:"comment:返回状态码"`
	ResponseTime float64   `json:"response_time" gorm:"comment:响应时长(毫秒)"`
	RequestBody  string    `json:"request_body" gorm:"type:text;comment:请求体"`
	ResponseBody string    `json:"response_body" gorm:"type:text;comment:响应体"`
}

// TableName 指定表名
func (APICallLog) TableName() string {
	return "api_call_logs"
}

// AdminUser 管理员用户模型
type AdminUser struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	Username  string         `json:"username" gorm:"uniqueIndex;size:50;comment:用户名"`
	Password  string         `json:"-" gorm:"size:255;comment:密码哈希"`
	Role      string         `json:"role" gorm:"size:20;default:'admin';comment:角色"`
	Status    string         `json:"status" gorm:"type:enum('active','disabled');default:'active';comment:状态"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (AdminUser) TableName() string {
	return "admin_users"
}
