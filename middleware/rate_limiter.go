package middleware

import (
	"net/http"
	"solve-api/models"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// RateLimiter 速率限制器
type RateLimiter struct {
	visitors map[string]*Visitor
	mu       sync.RWMutex
	rate     int           // 每分钟允许的请求数
	capacity int           // 令牌桶容量
	cleanup  time.Duration // 清理间隔
}

// Visitor 访问者信息
type Visitor struct {
	tokens   int       // 当前令牌数
	lastSeen time.Time // 最后访问时间
}

// NewRateLimiter 创建速率限制器
func NewRateLimiter(rate, capacity int) *RateLimiter {
	rl := &RateLimiter{
		visitors: make(map[string]*Visitor),
		rate:     rate,
		capacity: capacity,
		cleanup:  time.Minute * 5, // 5分钟清理一次
	}
	
	// 启动清理协程
	go rl.cleanupVisitors()
	
	return rl
}

// Allow 检查是否允许请求
func (rl *RateLimiter) Allow(key string) bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()
	
	now := time.Now()
	visitor, exists := rl.visitors[key]
	
	if !exists {
		// 新访问者
		rl.visitors[key] = &Visitor{
			tokens:   rl.capacity - 1,
			lastSeen: now,
		}
		return true
	}
	
	// 计算应该添加的令牌数
	elapsed := now.Sub(visitor.lastSeen)
	tokensToAdd := int(elapsed.Minutes()) * rl.rate
	
	if tokensToAdd > 0 {
		visitor.tokens += tokensToAdd
		if visitor.tokens > rl.capacity {
			visitor.tokens = rl.capacity
		}
		visitor.lastSeen = now
	}
	
	if visitor.tokens > 0 {
		visitor.tokens--
		return true
	}
	
	return false
}

// cleanupVisitors 清理过期的访问者
func (rl *RateLimiter) cleanupVisitors() {
	ticker := time.NewTicker(rl.cleanup)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			rl.mu.Lock()
			now := time.Now()
			for key, visitor := range rl.visitors {
				if now.Sub(visitor.lastSeen) > rl.cleanup {
					delete(rl.visitors, key)
				}
			}
			rl.mu.Unlock()
		}
	}
}

// RateLimitMiddleware 速率限制中间件
func RateLimitMiddleware(rateLimiter *RateLimiter) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 使用 IP 地址作为限制键
		key := c.ClientIP()
		
		// 如果有 API Key，使用 API Key 作为限制键
		if apiKeyInterface, exists := c.Get("api_key"); exists {
			if apiKey, ok := apiKeyInterface.(*models.APIKey); ok {
				key = apiKey.Key
			}
		}
		
		if !rateLimiter.Allow(key) {
			c.JSON(http.StatusTooManyRequests, models.ErrorResponse{
				Code:    429,
				Message: "请求过于频繁，请稍后再试",
				Error:   "Rate limit exceeded",
			})
			c.Abort()
			return
		}
		
		c.Next()
	}
}
