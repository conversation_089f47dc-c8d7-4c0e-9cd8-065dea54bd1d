package middleware

import (
	"net/http"
	"solve-api/database"
	"solve-api/models"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware API Key 认证中间件
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取 Authorization header
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.<PERSON>(http.StatusUnauthorized, models.ErrorResponse{
				Code:    401,
				Message: "缺少 Authorization header",
			})
			c.Abort()
			return
		}

		// 检查 Bearer 格式
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.J<PERSON>(http.StatusUnauthorized, models.ErrorResponse{
				Code:    401,
				Message: "Authorization header 格式错误，应为 Bearer {API_KEY}",
			})
			c.Abort()
			return
		}

		// 提取 API Key
		apiKey := strings.TrimPrefix(authHeader, "Bearer ")
		if apiKey == "" {
			c.<PERSON>(http.StatusUnauthorized, models.ErrorResponse{
				Code:    401,
				Message: "API Key 不能为空",
			})
			c.Abort()
			return
		}

		// 验证 API Key
		var key models.APIKey
		if err := database.DB.Where("`key` = ? AND status = ?", apiKey, "active").First(&key).Error; err != nil {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Code:    401,
				Message: "无效的 API Key",
			})
			c.Abort()
			return
		}

		// 检查配额
		if key.QuotaUsed >= key.QuotaLimit {
			c.JSON(http.StatusTooManyRequests, models.ErrorResponse{
				Code:    429,
				Message: "API 调用配额已用完",
			})
			c.Abort()
			return
		}

		// 检查 IP 白名单（如果设置了）
		if key.IPWhitelist != "" {
			clientIP := c.ClientIP()
			allowedIPs := strings.Split(key.IPWhitelist, ",")
			allowed := false
			for _, ip := range allowedIPs {
				if strings.TrimSpace(ip) == clientIP {
					allowed = true
					break
				}
			}
			if !allowed {
				c.JSON(http.StatusForbidden, models.ErrorResponse{
					Code:    403,
					Message: "IP 地址不在白名单中",
				})
				c.Abort()
				return
			}
		}

		// 更新使用次数和最后使用时间
		now := time.Now()
		database.DB.Model(&key).Updates(models.APIKey{
			QuotaUsed:  key.QuotaUsed + 1,
			LastUsedAt: &now,
		})

		// 将 API Key 信息存储到上下文中
		c.Set("api_key", &key)
		c.Next()
	}
}
