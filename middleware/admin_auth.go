package middleware

import (
	"net/http"
	"solve-api/models"
	"solve-api/utils"
	"strings"

	"github.com/gin-gonic/gin"
)

// AdminAuthMiddleware 管理员认证中间件
func AdminAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取 Authorization header
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.J<PERSON>(http.StatusUnauthorized, models.ErrorResponse{
				Code:    401,
				Message: "缺少 Authorization header",
			})
			c.Abort()
			return
		}

		// 检查 Bearer 格式
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.J<PERSON>(http.StatusUnauthorized, models.ErrorResponse{
				Code:    401,
				Message: "Authorization header 格式错误，应为 Bearer {TOKEN}",
			})
			c.Abort()
			return
		}

		// 提取 Token
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			c.<PERSON>(http.StatusUnauthorized, models.ErrorResponse{
				Code:    401,
				Message: "Token 不能为空",
			})
			c.Abort()
			return
		}

		// 解析 Token
		claims, err := utils.ParseToken(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Code:    401,
				Message: "无效的 Token",
				Error:   err.Error(),
			})
			c.Abort()
			return
		}

		// 检查角色权限
		if claims.Role != "admin" && claims.Role != "super_admin" {
			c.JSON(http.StatusForbidden, models.ErrorResponse{
				Code:    403,
				Message: "权限不足",
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("admin_user_id", claims.UserID)
		c.Set("admin_username", claims.Username)
		c.Set("admin_role", claims.Role)
		c.Next()
	}
}
