package middleware

import (
	"bytes"
	"io"
	"solve-api/database"
	"solve-api/models"
	"time"

	"github.com/gin-gonic/gin"
)

// responseWriter 包装 gin.ResponseWriter 以捕获响应内容
type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w responseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// LoggingMiddleware API 调用日志记录中间件
func LoggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		// 读取请求体
		var requestBody []byte
		if c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 包装响应写入器
		w := &responseWriter{
			ResponseWriter: c.Writer,
			body:           bytes.NewBufferString(""),
		}
		c.Writer = w

		// 处理请求
		c.Next()

		// 计算响应时间
		duration := time.Since(start)

		// 获取 API Key 信息
		apiKeyInterface, exists := c.Get("api_key")
		if !exists {
			return // 如果没有 API Key 信息，不记录日志
		}

		apiKey, ok := apiKeyInterface.(*models.APIKey)
		if !ok {
			return
		}

		// 创建日志记录
		log := models.APICallLog{
			APIKeyID:     apiKey.ID,
			Path:         c.Request.URL.Path,
			Method:       c.Request.Method,
			IP:           c.ClientIP(),
			Timestamp:    start,
			StatusCode:   c.Writer.Status(),
			ResponseTime: float64(duration.Nanoseconds()) / 1e6, // 转换为毫秒
			RequestBody:  string(requestBody),
			ResponseBody: w.body.String(),
		}

		// 异步保存日志（避免影响响应时间）
		go func() {
			database.DB.Create(&log)
		}()
	}
}
