#!/bin/bash

# 构建脚本 - 用于生产环境部署

echo "开始构建 solve-api 项目..."

# 设置 Go 代理（中国大陆用户）
export GOPROXY=https://goproxy.cn,direct

# 清理之前的构建
echo "清理之前的构建文件..."
rm -f solve-api

# 下载依赖
echo "下载 Go 依赖..."
go mod tidy

# 构建二进制文件
echo "构建二进制文件..."
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-w -s" -o solve-api main.go

# 检查构建是否成功
if [ -f "solve-api" ]; then
    echo "✅ 构建成功！二进制文件: solve-api"
    echo "文件大小: $(du -h solve-api | cut -f1)"
else
    echo "❌ 构建失败！"
    exit 1
fi

echo "构建完成！"
