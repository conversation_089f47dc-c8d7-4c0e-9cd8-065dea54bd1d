{"info": {"name": "拍照搜题API v2.0", "description": "智能题目识别和解答API接口集合", "version": "2.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://your-domain.com", "type": "string"}, {"key": "api_key", "value": "your_api_key_here", "type": "string"}], "item": [{"name": "健康检查", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "检查API服务状态"}, "response": [{"name": "成功响应", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"status\": \"ok\",\n  \"timestamp\": \"2025-06-04T10:30:00Z\"\n}"}]}, {"name": "题目解析 - 选择题", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{api_key}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"qu_type\": \"1\",\n  \"img_url\": \"https://example.com/question.jpg\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/solve", "host": ["{{base_url}}"], "path": ["api", "solve"]}, "description": "解析选择题图片"}, "response": [{"name": "成功响应", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{api_key}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"qu_type\": \"1\",\n  \"img_url\": \"https://example.com/question.jpg\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/solve", "host": ["{{base_url}}"], "path": ["api", "solve"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"code\": 200,\n  \"message\": \"解析成功\",\n  \"data\": {\n    \"type\": \"选择题\",\n    \"question\": \"题目内容\",\n    \"options\": {\n      \"A\": \"选项A内容\",\n      \"B\": \"选项B内容\",\n      \"C\": \"选项C内容\",\n      \"D\": \"选项D内容\"\n    },\n    \"answer\": \"A\",\n    \"analysis\": \"详细解析内容...\"\n  }\n}"}]}, {"name": "题目解析 - 填空题", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{api_key}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"qu_type\": \"2\",\n  \"img_url\": \"https://example.com/fill-question.jpg\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/solve", "host": ["{{base_url}}"], "path": ["api", "solve"]}, "description": "解析填空题图片"}}, {"name": "题目解析 - 判断题", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{api_key}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"qu_type\": \"3\",\n  \"img_url\": \"https://example.com/judge-question.jpg\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/solve", "host": ["{{base_url}}"], "path": ["api", "solve"]}, "description": "解析判断题图片"}}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 在这里可以添加预请求脚本", "console.log('发送请求到拍照搜题API...');"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// 通用测试脚本", "pm.test('状态码为200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('响应时间小于30秒', function () {", "    pm.expect(pm.response.responseTime).to.be.below(30000);", "});", "", "pm.test('响应格式为JSON', function () {", "    pm.response.to.be.json;", "});"]}}]}