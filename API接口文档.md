# 题库搜题解析 API 接口文档

## 📋 概述

本 API 服务提供基于 AI 的题库搜题解析功能，支持图片识别和智能解答。通过上传题目图片，系统将自动识别题目内容、选项，并提供正确答案和详细解析。

### 🌐 服务地址
```
生产环境：http://************:8080
```

### 🔑 认证方式
使用 API Key 进行身份认证，需要在请求头中携带 `Authorization: Bearer {API_KEY}`

---

## 🚀 快速开始

### 1. 获取 API Key
请联系管理员获取您的专属 API Key。
ak_a3d830754e42dde3c7936697e23af6779fbb68ce797060ab5096d68fae5c6b60//这个KEY可以用
### 2. 发起第一个请求
```bash
curl -X POST http://************:8080/api/solve \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "qu_type": "single",
    "img_url": "https://example.com/question.jpg"
  }'
```

---

## 📚 API 接口详情

### 🔍 题目解析接口

#### 基本信息
- **接口地址**：`POST /api/solve`
- **功能描述**：解析题目图片，返回题目内容、选项、答案和解析
- **请求方式**：POST
- **内容类型**：application/json

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| qu_type | string | 是 | 业务逻辑类型 | "1" |
| img_url | string | 是 | 题目图片URL | "https://example.com/image.jpg" |

**qu_type 支持的值：**
- `1`：图片识别解题（当前支持的业务逻辑）
- `2-6`：预留扩展（未来功能）

#### 请求示例

```json
{
  "qu_type": "1",
  "img_url": "https://tiku.uzdns.com/storage/images/20q4976.jpg"
}
```

#### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | int | 响应状态码，200表示成功 |
| message | string | 响应消息 |
| data | object | 解析结果数据 |

**data 对象结构：**

| 参数名 | 类型 | 说明 |
|--------|------|------|
| type | string | 题目类型（如：单选题、多选题、判断题） |
| question | string | 题目内容 |
| options | object | 选项内容，键为选项字母，值为选项内容 |
| answer | string | 正确答案 |
| analysis | string | 详细解析 |

#### 成功响应示例

```json
{
  "code": 200,
  "message": "解析成功",
  "data": {
    "type": "单选题",
    "question": "(图所示)驾驶机动车遇到这种情况时,以下做法正确的是什么?",
    "options": {
      "A": "应减速观察水情然后加速行驶通过。",
      "B": "可随意通行。",
      "C": "应停车察明水情确认安全后快速通过。",
      "D": "应停车察明水情确认安全后低速通过."
    },
    "answer": "D",
    "analysis": "在水中行车需要谨慎，避免因车轮打滑而失控或造成事故。因此，在不确定水域深度和情况的情况下应该先停下车来仔细查看并确保安全后再缓慢驶过积水区域以降低风险。所以选择 D 是正确的做法。"
  }
}
```

#### 错误响应示例

```json
{
  "code": 400,
  "message": "无效的业务逻辑类型",
  "error": "qu_type 当前只支持 1"
}
```

```json
{
  "code": 401,
  "message": "无效的 API Key"
}
```

```json
{
  "code": 429,
  "message": "API 调用配额已用完"
}
```

---

## 🔧 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 认证失败，API Key 无效 |
| 403 | 权限不足，IP 不在白名单中 |
| 429 | 请求频率超限，配额用完 |
| 500 | 服务器内部错误 |

---

## 💡 使用示例

### JavaScript (Node.js)

```javascript
const axios = require('axios');

async function solveQuestion(apiKey, imageUrl, businessType = '1') {
  try {
    const response = await axios.post('http://************:8080/api/solve', {
      qu_type: businessType,
      img_url: imageUrl
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('API 调用失败:', error.response?.data || error.message);
    throw error;
  }
}

// 使用示例
solveQuestion(
  'your_api_key_here',
  'https://example.com/question.jpg',
  '1'
).then(result => {
  console.log('解析结果:', result);
});
```

### Python

```python
import requests
import json

def solve_question(api_key, image_url, question_type='single'):
    url = 'http://************:8080/api/solve'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    data = {
        'qu_type': question_type,
        'img_url': image_url
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f'API 调用失败: {e}')
        raise

# 使用示例
result = solve_question(
    'your_api_key_here',
    'https://example.com/question.jpg',
    'single'
)
print('解析结果:', json.dumps(result, ensure_ascii=False, indent=2))
```

### PHP

```php
<?php
function solveQuestion($apiKey, $imageUrl, $questionType = 'single') {
    $url = 'http://************:8080/api/solve';
    $data = [
        'qu_type' => $questionType,
        'img_url' => $imageUrl
    ];
    
    $options = [
        'http' => [
            'header' => [
                'Authorization: Bearer ' . $apiKey,
                'Content-Type: application/json'
            ],
            'method' => 'POST',
            'content' => json_encode($data)
        ]
    ];
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    if ($result === FALSE) {
        throw new Exception('API 调用失败');
    }
    
    return json_decode($result, true);
}

// 使用示例
try {
    $result = solveQuestion(
        'your_api_key_here',
        'https://example.com/question.jpg',
        'single'
    );
    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
} catch (Exception $e) {
    echo '错误: ' . $e->getMessage();
}
?>
```

### Java

```java
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Map;

public class QuestionSolver {
    private static final String API_URL = "http://************:8080/api/solve";
    private final HttpClient client;
    private final ObjectMapper mapper;
    
    public QuestionSolver() {
        this.client = HttpClient.newHttpClient();
        this.mapper = new ObjectMapper();
    }
    
    public Map<String, Object> solveQuestion(String apiKey, String imageUrl, String questionType) 
            throws Exception {
        Map<String, String> requestData = Map.of(
            "qu_type", questionType,
            "img_url", imageUrl
        );
        
        String jsonBody = mapper.writeValueAsString(requestData);
        
        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(API_URL))
            .header("Authorization", "Bearer " + apiKey)
            .header("Content-Type", "application/json")
            .POST(HttpRequest.BodyPublishers.ofString(jsonBody))
            .build();
            
        HttpResponse<String> response = client.send(request, 
            HttpResponse.BodyHandlers.ofString());
            
        return mapper.readValue(response.body(), Map.class);
    }
    
    // 使用示例
    public static void main(String[] args) throws Exception {
        QuestionSolver solver = new QuestionSolver();
        Map<String, Object> result = solver.solveQuestion(
            "your_api_key_here",
            "https://example.com/question.jpg",
            "single"
        );
        System.out.println(result);
    }
}
```

---

## ⚠️ 注意事项

### 图片要求
- **格式支持**：JPG、PNG、GIF、WebP
- **大小限制**：建议不超过 10MB
- **分辨率**：建议宽度不超过 2000px
- **内容要求**：图片应清晰可读，包含完整的题目和选项

### 请求限制
- **并发限制**：建议单个 API Key 并发请求不超过 5 个
- **超时时间**：单次请求超时时间为 60 秒
- **配额限制**：根据您的套餐限制每月调用次数

### 最佳实践
1. **错误处理**：请妥善处理各种错误状态码
2. **重试机制**：网络异常时建议实现指数退避重试
3. **缓存策略**：相同图片可以缓存结果，避免重复调用
4. **监控告警**：建议监控 API 调用成功率和响应时间

---

## 🆘 技术支持

### 常见问题

**Q: API Key 如何获取？**
A: 请联系管理员申请 API Key，会为您分配专属的密钥和配额。

**Q: 支持哪些类型的题目？**
A: 目前支持单选题、多选题和判断题，后续会扩展更多题型。

**Q: 图片 URL 有什么要求？**
A: 必须是公网可访问的 HTTP/HTTPS URL，图片格式支持常见的图片格式。

**Q: 如何查看 API 使用情况？**
A: 管理员可以通过管理后台查看详细的调用日志和统计信息。

### 联系方式
如有技术问题或需要支持，请联系技术团队。

---

## 📝 更新日志

### v1.0.0 (2024-12-06)
- 🎉 首次发布
- ✅ 支持单选题、多选题、判断题解析
- ✅ 基于阿里云 Qwen-VL 大模型
- ✅ API Key 认证和配额管理
- ✅ 完整的错误处理和日志记录
