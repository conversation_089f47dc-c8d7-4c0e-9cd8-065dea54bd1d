[Unit]
Description=Solve API Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=www
Group=www
WorkingDirectory=/www/wwwroot/solve-api
ExecStart=/www/wwwroot/solve-api/solve-api
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=5
PrivateTmp=true
Restart=on-failure
RestartSec=10

# 环境变量
Environment=GIN_MODE=release

# 日志
StandardOutput=journal
StandardError=journal
SyslogIdentifier=solve-api

[Install]
WantedBy=multi-user.target
