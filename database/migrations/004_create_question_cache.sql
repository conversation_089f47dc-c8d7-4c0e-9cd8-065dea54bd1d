-- 题目缓存表 - 用于持久化缓存数据
CREATE TABLE IF NOT EXISTS `question_cache` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `cache_key` varchar(64) NOT NULL COMMENT '缓存键（MD5哈希）',
  `question_type` varchar(20) NOT NULL COMMENT '题目类型',
  `question_content` text NOT NULL COMMENT '题目内容',
  `question_options` json NOT NULL COMMENT '题目选项（JSON格式）',
  `answer` varchar(500) NOT NULL COMMENT '正确答案',
  `analysis` text NOT NULL COMMENT '答案解析',
  `hit_count` int(11) NOT NULL DEFAULT '0' COMMENT '命中次数',
  `last_hit_at` timestamp NULL DEFAULT NULL COMMENT '最后命中时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_cache_key` (`cache_key`),
  KEY `idx_question_type` (`question_type`),
  KEY `idx_hit_count` (`hit_count`),
  KEY `idx_last_hit_at` (`last_hit_at`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='题目缓存表';

-- 缓存统计表
CREATE TABLE IF NOT EXISTS `cache_stats` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL COMMENT '统计日期',
  `redis_hits` int(11) NOT NULL DEFAULT '0' COMMENT 'Redis命中次数',
  `mysql_hits` int(11) NOT NULL DEFAULT '0' COMMENT 'MySQL命中次数',
  `cache_misses` int(11) NOT NULL DEFAULT '0' COMMENT '缓存未命中次数',
  `total_requests` int(11) NOT NULL DEFAULT '0' COMMENT '总请求次数',
  `hit_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '命中率(%)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='缓存统计表';
