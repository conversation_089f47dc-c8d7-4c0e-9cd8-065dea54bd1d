#!/bin/bash

# 停止脚本 - 用于生产环境

APP_NAME="solve-api"
PID_FILE="/tmp/${APP_NAME}.pid"

# 检查 PID 文件是否存在
if [ ! -f "$PID_FILE" ]; then
    echo "❌ PID 文件不存在，$APP_NAME 可能未运行"
    exit 1
fi

# 读取 PID
PID=$(cat $PID_FILE)

# 检查进程是否存在
if ! ps -p $PID > /dev/null 2>&1; then
    echo "❌ 进程 $PID 不存在，清理 PID 文件"
    rm -f $PID_FILE
    exit 1
fi

echo "停止 $APP_NAME (PID: $PID)..."

# 发送 TERM 信号
kill $PID

# 等待进程结束
for i in {1..10}; do
    if ! ps -p $PID > /dev/null 2>&1; then
        echo "✅ $APP_NAME 已停止"
        rm -f $PID_FILE
        exit 0
    fi
    echo "等待进程结束... ($i/10)"
    sleep 1
done

# 如果进程仍然存在，强制杀死
echo "⚠️  进程未正常结束，强制停止..."
kill -9 $PID

if ! ps -p $PID > /dev/null 2>&1; then
    echo "✅ $APP_NAME 已强制停止"
    rm -f $PID_FILE
else
    echo "❌ 无法停止 $APP_NAME"
    exit 1
fi
