package cmd

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"log"
	"solve-api/config"
	"solve-api/database"
	"solve-api/models"
)

func CreateAPIKey() {
	// 加载配置
	config.LoadConfig()

	// 初始化数据库
	database.InitDatabase()

	// 生成 API Key
	apiKey := generateAPIKey()

	// 创建 API Key 记录
	key := models.APIKey{
		Key:         apiKey,
		Status:      "active",
		QuotaLimit:  1000,
		QuotaUsed:   0,
		IPWhitelist: "", // 空表示不限制 IP
	}

	if err := database.DB.Create(&key).Error; err != nil {
		log.Fatalf("创建 API Key 失败: %v", err)
	}

	fmt.Printf("API Key 创建成功!\n")
	fmt.Printf("API Key: %s\n", apiKey)
	fmt.Printf("配额限制: %d 次/月\n", key.QuotaLimit)
	fmt.Printf("状态: %s\n", key.Status)
	fmt.Printf("\n使用方法:\n")
	fmt.Printf("curl -X POST http://localhost:8080/api/solve \\\n")
	fmt.Printf("  -H \"Authorization: Bearer %s\" \\\n", apiKey)
	fmt.Printf("  -H \"Content-Type: application/json\" \\\n")
	fmt.Printf("  -d '{\n")
	fmt.Printf("    \"qu_type\": \"single\",\n")
	fmt.Printf("    \"img_url\": \"https://tiku.uzdns.com/storage/images/20q4976.jpg\"\n")
	fmt.Printf("  }'\n")
}

// generateAPIKey 生成随机 API Key
func generateAPIKey() string {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		log.Fatalf("生成随机数失败: %v", err)
	}
	return "ak_" + hex.EncodeToString(bytes)
}
