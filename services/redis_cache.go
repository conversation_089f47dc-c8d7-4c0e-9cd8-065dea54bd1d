package services

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"sort"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"solve-api/config"
	"solve-api/utils"
)

// QuestionCacheData 缓存的完整题目数据
type QuestionCacheData struct {
	Type     string            `json:"type"`
	Question string            `json:"question"`
	Options  map[string]string `json:"options"`
	Answer   string            `json:"answer"`
	Analysis string            `json:"analysis"`
}

// RedisCache Redis缓存服务
type RedisCache struct {
	client *redis.Client
	ctx    context.Context
	ttl    time.Duration
}

// NewRedisCache 创建Redis缓存实例
func NewRedisCache() *RedisCache {
	cfg := config.AppConfig.Redis
	
	rdb := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%s", cfg.Host, cfg.Port),
		Password: cfg.Password,
		DB:       cfg.DB,
	})
	
	// 测试连接
	ctx := context.Background()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		log.Printf("Redis连接失败: %v", err)
		return nil
	}
	
	log.Println("Redis连接成功")
	
	return &RedisCache{
		client: rdb,
		ctx:    ctx,
		ttl:    0, // 永久不过期
	}
}

// GenerateQuestionCacheKey 基于题目内容生成缓存键
func (r *RedisCache) GenerateQuestionCacheKey(questionType, question string, options map[string]string) string {
	// 使用统一的文本规范化工具
	normalizedQuestion := utils.NormalizeForCache(question)

	// 标准化选项内容
	normalizedOptions := r.normalizeOptions(options)

	// 组合生成键
	data := questionType + "|" + normalizedQuestion + "|" + normalizedOptions
	hash := md5.Sum([]byte(data))

	// 添加命名空间前缀
	return fmt.Sprintf("question:cache:%s", hex.EncodeToString(hash[:]))
}

// normalizeText 标准化文本内容
// 注意：此函数已被utils.NormalizeForCache替代，保留用于兼容性
func (r *RedisCache) normalizeText(text string) string {
	return utils.NormalizeForCache(text)
}

// normalizeOptions 标准化选项内容
func (r *RedisCache) normalizeOptions(options map[string]string) string {
	if len(options) == 0 {
		return ""
	}

	// 将选项按键排序，确保一致性
	keys := make([]string, 0, len(options))
	for k := range options {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	var parts []string
	for _, key := range keys {
		normalizedValue := utils.NormalizeForCache(options[key])
		parts = append(parts, key+":"+normalizedValue)
	}

	return strings.Join(parts, "|")
}

// Get 获取缓存的完整题目数据
func (r *RedisCache) Get(key string) (*QuestionCacheData, bool) {
	if r.client == nil {
		return nil, false
	}

	val, err := r.client.Get(r.ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			// 缓存未命中
			return nil, false
		}
		log.Printf("Redis获取缓存失败: %v", err)
		return nil, false
	}

	var data QuestionCacheData
	if err := json.Unmarshal([]byte(val), &data); err != nil {
		log.Printf("Redis缓存数据解析失败: %v", err)
		return nil, false
	}

	return &data, true
}

// Set 设置完整题目数据缓存
func (r *RedisCache) Set(key, questionType, question string, options map[string]string, answer, analysis string) error {
	if r.client == nil {
		return fmt.Errorf("Redis客户端未初始化")
	}

	data := QuestionCacheData{
		Type:     questionType,
		Question: question,
		Options:  options,
		Answer:   answer,
		Analysis: analysis,
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化缓存数据失败: %v", err)
	}

	// 设置缓存，如果ttl为0则永久不过期
	if r.ttl == 0 {
		err = r.client.Set(r.ctx, key, jsonData, 0).Err()
	} else {
		err = r.client.Set(r.ctx, key, jsonData, r.ttl).Err()
	}

	if err != nil {
		log.Printf("Redis设置缓存失败: %v", err)
		return err
	}

	log.Printf("Redis缓存设置成功: %s", key)
	return nil
}

// Delete 删除缓存
func (r *RedisCache) Delete(key string) error {
	if r.client == nil {
		return fmt.Errorf("Redis客户端未初始化")
	}
	
	return r.client.Del(r.ctx, key).Err()
}

// GetStats 获取缓存统计
func (r *RedisCache) GetStats() map[string]interface{} {
	if r.client == nil {
		return map[string]interface{}{
			"status": "disconnected",
		}
	}
	
	info, err := r.client.Info(r.ctx, "memory").Result()
	if err != nil {
		return map[string]interface{}{
			"status": "error",
			"error":  err.Error(),
		}
	}
	
	// 获取键数量
	keyCount, _ := r.client.DBSize(r.ctx).Result()
	
	ttlInfo := "永久不过期"
	if r.ttl > 0 {
		ttlInfo = fmt.Sprintf("%.1f小时", r.ttl.Hours())
	}

	return map[string]interface{}{
		"status":      "connected",
		"key_count":   keyCount,
		"ttl_setting": ttlInfo,
		"memory_info": info,
	}
}

// Clear 清空指定模式的缓存
func (r *RedisCache) Clear(pattern string) error {
	if r.client == nil {
		return fmt.Errorf("Redis客户端未初始化")
	}
	
	if pattern == "" {
		pattern = "question:cache:*"
	}
	
	keys, err := r.client.Keys(r.ctx, pattern).Result()
	if err != nil {
		return err
	}
	
	if len(keys) > 0 {
		return r.client.Del(r.ctx, keys...).Err()
	}
	
	return nil
}

// Close 关闭Redis连接
func (r *RedisCache) Close() error {
	if r.client != nil {
		return r.client.Close()
	}
	return nil
}
