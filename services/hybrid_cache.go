package services

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"log"
	"sort"
	"strings"
	"time"

	"solve-api/database"
	"solve-api/models"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

// HybridCache 混合缓存服务（Redis + MySQL）
type HybridCache struct {
	redis *redis.Client
	db    *gorm.DB
	ctx   context.Context
}

// NewHybridCache 创建混合缓存实例
func NewHybridCache() *HybridCache {
	redisCache := NewRedisCache()
	
	return &HybridCache{
		redis: redisCache.client,
		db:    database.DB,
		ctx:   context.Background(),
	}
}

// generateCacheKey 生成缓存键
func (hc *HybridCache) generateCacheKey(questionData *models.QuestionData) string {
	// 创建一个包含所有相关信息的字符串
	var parts []string
	parts = append(parts, "type:"+questionData.Type)
	parts = append(parts, "question:"+questionData.Question)

	// 对选项进行排序以确保一致性
	var optionParts []string
	for key, value := range questionData.Options {
		optionParts = append(optionParts, fmt.Sprintf("%s:%s", key, value))
	}
	sort.Strings(optionParts)
	parts = append(parts, "options:"+strings.Join(optionParts, "|"))

	// 生成MD5哈希
	content := strings.Join(parts, "||")
	hash := md5.Sum([]byte(content))
	return fmt.Sprintf("%x", hash)
}

// generateCacheKeyFromRawData 基于原始数据生成缓存键
func (hc *HybridCache) generateCacheKeyFromRawData(rawData string) string {
	// 直接对原始数据生成MD5哈希
	hash := md5.Sum([]byte(rawData))
	return fmt.Sprintf("%x", hash)
}

// Get 获取缓存数据（先Redis，后MySQL）
func (hc *HybridCache) Get(questionData *models.QuestionData) (*models.QuestionData, *models.CacheHitInfo, error) {
	cacheKey := hc.generateCacheKey(questionData)
	redisKey := "question:cache:" + cacheKey
	
	// 1. 尝试从Redis获取
	if hc.redis != nil {
		redisData, err := hc.redis.Get(hc.ctx, redisKey).Result()
		if err == nil {
			var cachedData models.QuestionData
			if err := json.Unmarshal([]byte(redisData), &cachedData); err == nil {
				log.Printf("✅ Redis缓存命中: %s", cacheKey)
				
				// 异步更新MySQL命中统计
				go hc.updateMySQLHitCount(cacheKey)
				go hc.updateCacheStats(models.CacheTypeRedis)
				
				return &cachedData, &models.CacheHitInfo{
					Type:     models.CacheTypeRedis,
					Key:      cacheKey,
					HitTime:  time.Now(),
					Source:   "Redis",
				}, nil
			}
		}
	}
	
	// 2. 尝试从MySQL获取
	var mysqlCache models.QuestionCache
	err := hc.db.Where("cache_key = ?", cacheKey).First(&mysqlCache).Error
	if err == nil {
		log.Printf("✅ MySQL缓存命中: %s", cacheKey)
		
		// 更新命中次数
		go mysqlCache.IncrementHit(hc.db)
		go hc.updateCacheStats(models.CacheTypeMySQL)
		
		// 将数据重新写入Redis
		cachedData := mysqlCache.ToQuestionData()
		go hc.setRedisCache(redisKey, cachedData)
		
		return cachedData, &models.CacheHitInfo{
			Type:     models.CacheTypeMySQL,
			Key:      cacheKey,
			HitTime:  time.Now(),
			Source:   "MySQL",
			HitCount: mysqlCache.HitCount + 1,
		}, nil
	}
	
	// 3. 缓存未命中
	log.Printf("❌ 缓存未命中: %s", cacheKey)
	go hc.updateCacheStats(models.CacheTypeMiss)
	
	return nil, &models.CacheHitInfo{
		Type:    models.CacheTypeMiss,
		Key:     cacheKey,
		HitTime: time.Now(),
		Source:  "None",
	}, nil
}

// GetByRawData 基于原始数据获取缓存数据（先Redis，后MySQL）
func (hc *HybridCache) GetByRawData(rawData string) (*models.QuestionData, *models.CacheHitInfo, error) {
	cacheKey := hc.generateCacheKeyFromRawData(rawData)
	redisKey := "question:cache:" + cacheKey

	// 1. 尝试从Redis获取
	if hc.redis != nil {
		redisData, err := hc.redis.Get(hc.ctx, redisKey).Result()
		if err == nil {
			var cachedData models.QuestionData
			if err := json.Unmarshal([]byte(redisData), &cachedData); err == nil {
				log.Printf("✅ Redis缓存命中: %s", cacheKey)

				// 异步更新MySQL命中统计
				go hc.updateMySQLHitCount(cacheKey)
				go hc.updateCacheStats(models.CacheTypeRedis)

				return &cachedData, &models.CacheHitInfo{
					Type:     models.CacheTypeRedis,
					Key:      cacheKey,
					HitTime:  time.Now(),
					Source:   "Redis",
				}, nil
			}
		}
	}

	// 2. 尝试从MySQL获取
	var mysqlCache models.QuestionCache
	err := hc.db.Where("cache_key = ?", cacheKey).First(&mysqlCache).Error
	if err == nil {
		log.Printf("✅ MySQL缓存命中: %s", cacheKey)

		// 更新命中次数
		go mysqlCache.IncrementHit(hc.db)
		go hc.updateCacheStats(models.CacheTypeMySQL)

		// 将数据重新写入Redis
		cachedData := mysqlCache.ToQuestionData()
		go hc.setRedisCache(redisKey, cachedData)

		return cachedData, &models.CacheHitInfo{
			Type:     models.CacheTypeMySQL,
			Key:      cacheKey,
			HitTime:  time.Now(),
			Source:   "MySQL",
			HitCount: mysqlCache.HitCount + 1,
		}, nil
	}

	// 3. 缓存未命中
	log.Printf("❌ 缓存未命中: %s", cacheKey)
	go hc.updateCacheStats(models.CacheTypeMiss)

	return nil, &models.CacheHitInfo{
		Type:    models.CacheTypeMiss,
		Key:     cacheKey,
		HitTime: time.Now(),
		Source:  "None",
	}, nil
}

// SetByRawData 基于原始数据设置缓存数据（同时写入Redis和MySQL）
func (hc *HybridCache) SetByRawData(rawData string, questionData *models.QuestionData) error {
	cacheKey := hc.generateCacheKeyFromRawData(rawData)
	redisKey := "question:cache:" + cacheKey

	// 1. 写入MySQL（持久化存储）
	var mysqlCache models.QuestionCache
	err := hc.db.Where("cache_key = ?", cacheKey).First(&mysqlCache).Error

	if err == gorm.ErrRecordNotFound {
		// 创建新记录
		now := time.Now()
		mysqlCache = models.QuestionCache{
			CacheKey:        cacheKey,
			QuestionType:    questionData.Type,
			QuestionContent: questionData.Question,
			QuestionOptions: models.JSONMap(questionData.Options),
			Answer:          questionData.Answer,
			Analysis:        questionData.Analysis,
			HitCount:        0,
			LastHitAt:       &now,
			CreatedAt:       now,
			UpdatedAt:       now,
		}

		if err := hc.db.Create(&mysqlCache).Error; err != nil {
			log.Printf("❌ MySQL缓存设置失败: %v", err)
		} else {
			log.Printf("✅ MySQL缓存设置成功: %s", cacheKey)
		}
	} else if err == nil {
		// 更新现有记录
		now := time.Now()
		mysqlCache.QuestionType = questionData.Type
		mysqlCache.QuestionContent = questionData.Question
		mysqlCache.QuestionOptions = models.JSONMap(questionData.Options)
		mysqlCache.Answer = questionData.Answer
		mysqlCache.Analysis = questionData.Analysis
		mysqlCache.LastHitAt = &now
		mysqlCache.UpdatedAt = now

		if err := hc.db.Save(&mysqlCache).Error; err != nil {
			log.Printf("❌ MySQL缓存更新失败: %v", err)
		} else {
			log.Printf("✅ MySQL缓存更新成功: %s", cacheKey)
		}
	}

	// 2. 写入Redis（高速缓存）
	return hc.setRedisCache(redisKey, questionData)
}

// Set 设置缓存数据（同时写入Redis和MySQL）
func (hc *HybridCache) Set(questionData *models.QuestionData) error {
	cacheKey := hc.generateCacheKey(questionData)
	redisKey := "question:cache:" + cacheKey

	// 1. 写入MySQL（持久化存储）
	var mysqlCache models.QuestionCache
	err := hc.db.Where("cache_key = ?", cacheKey).First(&mysqlCache).Error

	if err == gorm.ErrRecordNotFound {
		// 创建新记录
		now := time.Now()
		mysqlCache = models.QuestionCache{
			CacheKey:        cacheKey,
			QuestionType:    questionData.Type,
			QuestionContent: questionData.Question,
			QuestionOptions: models.JSONMap(questionData.Options),
			Answer:          questionData.Answer,
			Analysis:        questionData.Analysis,
			HitCount:        0,
			LastHitAt:       &now,
			CreatedAt:       now,
			UpdatedAt:       now,
		}

		if err := hc.db.Create(&mysqlCache).Error; err != nil {
			log.Printf("❌ MySQL缓存设置失败: %v", err)
		} else {
			log.Printf("✅ MySQL缓存设置成功: %s", cacheKey)
		}
	} else if err == nil {
		// 更新现有记录
		now := time.Now()
		mysqlCache.QuestionType = questionData.Type
		mysqlCache.QuestionContent = questionData.Question
		mysqlCache.QuestionOptions = models.JSONMap(questionData.Options)
		mysqlCache.Answer = questionData.Answer
		mysqlCache.Analysis = questionData.Analysis
		mysqlCache.LastHitAt = &now
		mysqlCache.UpdatedAt = now

		if err := hc.db.Save(&mysqlCache).Error; err != nil {
			log.Printf("❌ MySQL缓存更新失败: %v", err)
		} else {
			log.Printf("✅ MySQL缓存更新成功: %s", cacheKey)
		}
	}

	// 2. 写入Redis（高速缓存）
	return hc.setRedisCache(redisKey, questionData)
}

// setRedisCache 设置Redis缓存
func (hc *HybridCache) setRedisCache(redisKey string, data *models.QuestionData) error {
	if hc.redis == nil {
		return fmt.Errorf("Redis不可用")
	}
	
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化缓存数据失败: %v", err)
	}
	
	// 设置永久缓存
	err = hc.redis.Set(hc.ctx, redisKey, jsonData, 0).Err()
	if err != nil {
		log.Printf("❌ Redis缓存设置失败: %v", err)
		return err
	}
	
	log.Printf("✅ Redis缓存设置成功: %s", redisKey)
	return nil
}

// updateMySQLHitCount 更新MySQL命中次数
func (hc *HybridCache) updateMySQLHitCount(cacheKey string) {
	var mysqlCache models.QuestionCache
	if err := hc.db.Where("cache_key = ?", cacheKey).First(&mysqlCache).Error; err == nil {
		mysqlCache.IncrementHit(hc.db)
	}
}

// updateCacheStats 更新缓存统计
func (hc *HybridCache) updateCacheStats(cacheType models.CacheType) {
	today := time.Now().Format("2006-01-02")
	date, _ := time.Parse("2006-01-02", today)
	
	var stats models.CacheStats
	err := hc.db.Where("date = ?", date).First(&stats).Error
	
	if err == gorm.ErrRecordNotFound {
		// 创建新的统计记录
		stats = models.CacheStats{
			Date: date,
		}
	}
	
	// 更新统计数据
	switch cacheType {
	case models.CacheTypeRedis:
		stats.RedisHits++
	case models.CacheTypeMySQL:
		stats.MySQLHits++
	case models.CacheTypeMiss:
		stats.CacheMisses++
	}
	
	stats.TotalRequests = stats.RedisHits + stats.MySQLHits + stats.CacheMisses
	if stats.TotalRequests > 0 {
		stats.HitRate = float64(stats.RedisHits+stats.MySQLHits) / float64(stats.TotalRequests) * 100
	}
	
	if err == gorm.ErrRecordNotFound {
		hc.db.Create(&stats)
	} else {
		hc.db.Save(&stats)
	}
}

// GetStats 获取缓存统计信息
func (hc *HybridCache) GetStats() map[string]interface{} {
	stats := make(map[string]interface{})
	
	// Redis状态
	if hc.redis != nil {
		_, err := hc.redis.Ping(hc.ctx).Result()
		if err == nil {
			stats["redis_status"] = "connected"
			
			// Redis内存信息
			info, _ := hc.redis.Info(hc.ctx, "memory").Result()
			stats["redis_memory"] = info
			
			// Redis键数量
			keyCount, _ := hc.redis.DBSize(hc.ctx).Result()
			stats["redis_keys"] = keyCount
		} else {
			stats["redis_status"] = "disconnected"
		}
	} else {
		stats["redis_status"] = "unavailable"
	}
	
	// MySQL缓存统计
	var mysqlStats struct {
		TotalRecords int64   `json:"total_records"`
		TotalHits    int64   `json:"total_hits"`
		AvgHitCount  float64 `json:"avg_hit_count"`
	}

	hc.db.Model(&models.QuestionCache{}).Count(&mysqlStats.TotalRecords)
	hc.db.Model(&models.QuestionCache{}).Select("SUM(hit_count)").Scan(&mysqlStats.TotalHits)
	hc.db.Model(&models.QuestionCache{}).Select("AVG(hit_count)").Scan(&mysqlStats.AvgHitCount)
	
	stats["mysql_cache"] = mysqlStats
	
	// 今日统计
	today := time.Now().Format("2006-01-02")
	date, _ := time.Parse("2006-01-02", today)
	
	var todayStats models.CacheStats
	hc.db.Where("date = ?", date).First(&todayStats)
	stats["today_stats"] = todayStats
	
	// 缓存配置
	stats["cache_type"] = "hybrid"
	stats["persistence"] = "mysql"
	stats["high_speed"] = "redis"
	
	return stats
}

// ClearCache 清理缓存
func (hc *HybridCache) ClearCache() error {
	var errors []string
	
	// 清理Redis缓存
	if hc.redis != nil {
		keys, err := hc.redis.Keys(hc.ctx, "question:cache:*").Result()
		if err == nil && len(keys) > 0 {
			deleted, err := hc.redis.Del(hc.ctx, keys...).Result()
			if err != nil {
				errors = append(errors, fmt.Sprintf("Redis清理失败: %v", err))
			} else {
				log.Printf("✅ Redis缓存清理成功，删除 %d 个键", deleted)
			}
		}
	}
	
	// 清理MySQL缓存（可选，通常不建议删除持久化数据）
	// 这里只清理Redis，保留MySQL作为持久化存储
	
	if len(errors) > 0 {
		return fmt.Errorf("缓存清理部分失败: %s", strings.Join(errors, "; "))
	}
	
	return nil
}
